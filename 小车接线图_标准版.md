# 🔌 MSPM0小车接线图 - 标准版

## 📋 **系统概览**
- **主控**: MSPM0G350X开发板
- **电源**: 双电源模块供电方案
- **电机**: TB6612双电机驱动
- **传感器**: MPU6050 + 编码器 + 灰度传感器
- **显示**: OLED屏幕（可选）

---

## ⚡ **电源系统接线**

### **电池连接**
```
电池正极 ──→ 电源模块1正极
电池正极 ──→ 电源模块2正极
电池负极 ──→ 电源模块1负极  
电池负极 ──→ 电源模块2负极
```

### **电源模块1 (5V供电)**
| 电源模块1 | MSPM0开发板 | 说明 |
|-----------|-------------|------|
| 5V (×2)   | 5V (×2)     | 主要5V供电 |
| GND (×2)  | GND (×2)    | 公共地线 |

### **电源模块2 (12V供电)**
| 电源模块2 | 目标设备 | 说明 |
|-----------|----------|------|
| 12V       | TB6612 VM | 电机驱动电源 |
| GND       | TB6612 GND | 驱动地线 |

---

## 🎮 **按键接线**

| 按键功能 | MSPM0引脚 | 轮趣底板标号 | 接线方式 |
|----------|-----------|--------------|----------|
| 按键1    | PA8       | PA0          | 接GND表示按下 |
| 按键2    | PB7       | PB11         | 接3.3V表示按下 |

---

## 🔍 **灰度传感器接线 (6路)**

### **电源连接**
| 灰度传感器 | 连接目标 |
|------------|----------|
| VCC        | 电源模块1的5V |
| GND        | 电源模块1的GND |

### **信号连接**
| 灰度传感器 | MSPM0引脚 | 功能 |
|------------|-----------|------|
| OUT1       | PA9       | 灰度1 |
| OUT3       | PA27      | 灰度3 |
| OUT4       | PA24      | 灰度4 |
| OUT5       | PB16      | 灰度5 |
| OUT6       | PA12      | 灰度6 |
| OUT8       | PB6       | 灰度8 |

---

## 🚗 **电机驱动系统 (TB6612)**

### **电源连接**
| TB6612引脚 | 连接目标 | 说明 |
|------------|----------|------|
| VM         | 电源模块2的12V | 电机电源 |
| VCC        | 电源模块1的5V  | 逻辑电源 |
| GND (×3)   | 电源模块1的GND | 公共地线 |

### **电机连接**
| TB6612引脚 | 连接目标 | 说明 |
|------------|----------|------|
| AO1        | 右轮电机正极 | 通过编码器板引出 |
| AO2        | 右轮电机负极 | 通过编码器板引出 |
| BO1        | 左轮电机正极 | 通过编码器板引出 |
| BO2        | 左轮电机负极 | 通过编码器板引出 |

### **控制信号连接**
| TB6612引脚 | MSPM0引脚 | 功能 |
|------------|-----------|------|
| PWMA       | PB2       | 右电机PWM |
| AIN1       | PA13      | 右电机方向1 |
| AIN2       | PA14      | 右电机方向2 |
| PWMB       | PB3       | 左电机PWM |
| BIN1       | PA16      | 左电机方向1 |
| BIN2       | PA17      | 左电机方向2 |
| STBY       | 5V        | 待机控制(常高) |

---

## 📐 **MPU6050陀螺仪接线**

### **电源连接**
| MPU6050引脚 | 连接目标 |
|-------------|----------|
| VCC         | 电源模块1的5V |
| GND         | 电源模块1的GND |

### **通信连接**
| MPU6050引脚 | MSPM0引脚 | 功能 |
|-------------|-----------|------|
| SCL         | PA1       | I2C时钟线 |
| SDA         | PA0       | I2C数据线 |
| INT         | PA7       | 中断信号 |

---

## 🔄 **编码器接线**

| 编码器 | MSPM0引脚 | 功能 |
|--------|-----------|------|
| 右轮编码器A相 | PA26 | 右轮计数A |
| 右轮编码器B相 | PA25 | 右轮计数B |
| 左轮编码器A相 | PB24 | 左轮计数A |
| 左轮编码器B相 | PB20 | 左轮计数B |

---

## 🖥️ **OLED显示屏接线 (可选)**

### **电源连接**
| OLED引脚 | 连接目标 |
|----------|----------|
| VCC      | 3.3V |
| GND      | GND |

### **通信连接 (软件I2C)**
| OLED引脚 | MSPM0引脚 | 功能 |
|----------|-----------|------|
| SCL      | PA28      | 软件I2C时钟 |
| SDA      | PA31      | 软件I2C数据 |

---

## 🔧 **引脚功能对照表**

### **MSPM0关键引脚分配**
| 引脚 | 功能 | 连接设备 |
|------|------|----------|
| PA0  | I2C_SDA | MPU6050 |
| PA1  | I2C_SCL | MPU6050 |
| PA7  | GPIO_INT | MPU6050中断 |
| PA8  | GPIO_KEY | 按键1 |
| PA9  | ADC | 灰度1 |
| PA12 | ADC | 灰度6 |
| PA13 | GPIO | 右电机方向1 |
| PA14 | GPIO | 右电机方向2 |
| PA16 | GPIO | 左电机方向1 |
| PA17 | GPIO | 左电机方向2 |
| PA24 | ADC | 灰度4 |
| PA25 | GPIO_INT | 右轮编码器B |
| PA26 | GPIO_INT | 右轮编码器A |
| PA27 | ADC | 灰度3 |
| PA28 | GPIO | OLED_SCL |
| PA31 | GPIO | OLED_SDA |
| PB2  | PWM | 右电机PWM |
| PB3  | PWM | 左电机PWM |
| PB6  | ADC | 灰度8 |
| PB7  | GPIO_KEY | 按键2 |
| PB16 | ADC | 灰度5 |
| PB20 | GPIO_INT | 左轮编码器B |
| PB24 | GPIO_INT | 左轮编码器A |

---

## ⚠️ **接线注意事项**

### **电源安全**
1. **电压匹配**: 确保5V设备接5V，3.3V设备接3.3V
2. **电流容量**: 电源模块1需提供足够5V电流(建议≥2A)
3. **地线连接**: 所有GND必须连接到公共地
4. **电源隔离**: 12V电机电源与5V逻辑电源分离

### **信号完整性**
1. **I2C上拉**: PA0/PA1需要4.7kΩ上拉电阻
2. **编码器滤波**: 编码器信号线建议加100nF滤波电容
3. **PWM频率**: 确保PWM频率设置为10kHz
4. **中断优先级**: MPU6050中断优先级设为最高

### **机械安装**
1. **编码器对齐**: 确保编码器与电机轴同心
2. **传感器位置**: MPU6050安装在小车重心位置
3. **线缆固定**: 所有连接线缆需要可靠固定
4. **接插件**: 建议使用可靠的接插件，避免虚接

---

## 🔍 **故障排查对照**

| 故障现象 | 可能原因 | 检查要点 |
|----------|----------|----------|
| 上电无反应 | 电源问题 | 检查5V供电和GND连接 |
| 电机不转 | PWM或方向控制 | 检查PB2/PB3和PA13-17 |
| 编码器无计数 | 中断配置 | 检查PA25/26和PB20/24 |
| MPU6050无响应 | I2C通信 | 检查PA0/PA1和上拉电阻 |
| 灰度无读数 | ADC配置 | 检查PA9/12/24/27和PB6/16 |

---

## 📝 **测试验证清单**

- [ ] 电源电压测试 (5V±0.1V, 12V±0.5V)
- [ ] PWM波形测试 (10kHz, 占空比可调)
- [ ] I2C通信测试 (MPU6050设备ID读取)
- [ ] 编码器中断测试 (手动转动计数)
- [ ] 电机方向测试 (正反转控制)
- [ ] 灰度传感器测试 (ADC数值读取)
- [ ] 按键功能测试 (电平变化检测)

**接线完成后，建议按照此清单逐项验证，确保系统正常工作。**
