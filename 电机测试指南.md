# 🔧 电机独立测试指南

## 🎯 **测试目标**
验证您修正后的接线是否正确，专门测试电机PWM和方向控制功能，不依赖MPU6050、编码器等其他模块。

---

## 📋 **测试前准备**

### **1. 确认接线 (关键8个引脚)**
```
电机方向控制：
TB6612_AIN1 → MSPM0_PA16 ✅
TB6612_AIN2 → MSPM0_PA17 ✅  
TB6612_BIN1 → MSPM0_PA14 ✅
TB6612_BIN2 → MSPM0_PA13 ✅

PWM控制：
TB6612_PWMA → MSPM0_PB2 ✅
TB6612_PWMB → MSPM0_PB3 ✅

按键控制：
按键1 → MSPM0_PA8 (接GND表示按下) ✅

LED指示：
LED → MSPM0_PB1 ✅
```

### **2. 电源连接确认**
```
TB6612电源：
VM → 12V电源 (电机驱动电源)
VCC → 5V电源 (逻辑电源)  
GND → 公共地
STBY → 5V (使能信号)

MSPM0电源：
VCC → 3.3V
GND → 公共地
```

---

## 🚀 **编译和烧录步骤**

### **方法1: 使用CCS (推荐)**
1. 打开Code Composer Studio
2. 将`test_motor_only.c`替换原来的`empty.c`
3. 编译项目 (Ctrl+B)
4. 连接调试器烧录到MSPM0

### **方法2: 使用Keil**
1. 打开Keil项目
2. 将`test_motor_only.c`替换原来的`empty.c`
3. 编译项目 (F7)
4. 使用调试器下载程序

### **方法3: 临时测试 (快速)**
```c
// 在原来的empty.c的main函数开头添加以下代码进行快速测试：

int main(void) {
    SYSCFG_DL_init();
    DL_TimerA_startCounter(PWM_0_INST);
    
    // 简单电机测试代码
    while(1) {
        // 按键检测
        if(DL_GPIO_readPins(GPIO_GRP_0_KEY0_PORT, GPIO_GRP_0_KEY0_PIN) == 0) {
            // 左电机正转测试
            DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);
            DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);
            DL_TimerA_setCaptureCompareValue(PWM_0_INST, 2000, GPIO_PWM_0_C1_IDX);
            
            delay_ms(2000);
            
            // 停止
            DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C1_IDX);
            delay_ms(1000);
        }
    }
}
```

---

## 🔍 **测试流程**

### **1. 上电检查**
- ✅ LED快速闪烁5次 → 系统启动成功
- ✅ LED以1Hz频率闪烁 → 系统正常运行
- ❌ LED不亮或异常 → 检查电源和程序烧录

### **2. 按键启动测试**
- 按下PA8按键 (接地)
- LED快速闪烁10次 → 测试开始

### **3. 观察测试序列**
```
测试序列 (总时长约15秒)：
1. 停止状态 (1秒) → 电机不转
2. 左电机正转 (2秒) → 观察左轮转向
3. 左电机反转 (2秒) → 观察左轮反向
4. 右电机正转 (2秒) → 观察右轮转向  
5. 右电机反转 (2秒) → 观察右轮反向
6. 双电机前进 (2秒) → 小车前进
7. 双电机后退 (2秒) → 小车后退
8. 差速转向 (2秒) → 小车转弯
9. 停止状态 (1秒) → 电机停止
```

### **4. 测试完成**
- LED常亮2秒 → 测试完成
- 可重复按键进行多次测试

---

## ✅ **预期结果判断**

### **🎯 接线完全正确**
- 所有电机按预期方向转动
- 转速平稳，无异常噪音
- 前进/后退/转向动作正确
- LED指示正常

### **⚠️ 方向引脚错误**
- 电机转向与预期相反
- 例如：左电机正转变成反转
- 需要检查AIN1/AIN2或BIN1/BIN2接线

### **❌ PWM引脚错误**
- 电机完全不转
- 或者左右电机动作交换
- 需要检查PB2/PB3接线

### **🔋 电源问题**
- 电机转动无力
- 系统重启或LED异常
- 需要检查12V/5V电源

---

## 🛠️ **故障排除**

### **电机不转**
1. 检查TB6612的STBY引脚是否接5V
2. 检查12V电源是否正常
3. 检查PWM引脚PB2/PB3连接
4. 用万用表测量PWM输出电压

### **转向错误**
1. 检查方向控制引脚：
   - AIN1→PA16, AIN2→PA17
   - BIN1→PA14, BIN2→PA13
2. 交换电机正负极线

### **系统异常**
1. 检查3.3V电源是否稳定
2. 检查地线连接
3. 重新烧录程序

---

## 📊 **测试记录表**

| 测试项目 | 预期现象 | 实际现象 | 结果 |
|----------|----------|----------|------|
| 系统启动 | LED闪烁5次 |  | ✅/❌ |
| 左电机正转 | 左轮顺时针转 |  | ✅/❌ |
| 左电机反转 | 左轮逆时针转 |  | ✅/❌ |
| 右电机正转 | 右轮顺时针转 |  | ✅/❌ |
| 右电机反转 | 右轮逆时针转 |  | ✅/❌ |
| 双电机前进 | 小车前进 |  | ✅/❌ |
| 双电机后退 | 小车后退 |  | ✅/❌ |
| 差速转向 | 小车转弯 |  | ✅/❌ |

---

## 🎯 **测试完成后**

### **如果测试全部通过 ✅**
- 恭喜！您的接线完全正确
- 可以继续进行完整的平衡车测试
- 原来的程序应该能正常工作

### **如果有问题 ❌**
- 根据故障现象修正对应接线
- 重复测试直到全部通过
- 必要时可以联系我进一步分析

---

## 💡 **安全提示**

1. **低速测试** - PWM限制在25%以下，避免机械损伤
2. **短时间运行** - 每次测试不超过2秒，防止电机过热
3. **随时停止** - 如有异常立即断电
4. **固定小车** - 测试时固定小车，防止跑偏

老板，这个测试程序专门验证您修正的接线是否正确，完全独立于其他模块，结果非常直观！
