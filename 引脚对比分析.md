# 🔍 引脚对比分析报告

## ❌ **重要发现：引脚定义存在多处不一致！**

---

## 📊 **详细对比表**

| 功能模块 | 接线图引脚 | 代码实际引脚 | 状态 | 说明 |
|----------|------------|--------------|------|------|
| **PWM输出** | | | | |
| 右电机PWM | PB2 | PB2 | ✅ 一致 | PWMA |
| 左电机PWM | PB3 | PB3 | ✅ 一致 | PWMB |
| **电机方向控制** | | | | |
| 右电机AIN1 | PA13 | PA16 | ❌ **不一致** | 接线图错误 |
| 右电机AIN2 | PA14 | PA17 | ❌ **不一致** | 接线图错误 |
| 左电机BIN1 | PA16 | PA14 | ❌ **不一致** | 接线图错误 |
| 左电机BIN2 | PA17 | PA13 | ❌ **不一致** | 接线图错误 |
| **I2C通信** | | | | |
| MPU6050 SDA | PA0 | PA0 | ✅ 一致 | I2C数据线 |
| MPU6050 SCL | PA1 | PA1 | ✅ 一致 | I2C时钟线 |
| MPU6050 INT | PA7 | PA7 | ✅ 一致 | 中断信号 |
| **编码器** | | | | |
| 右轮编码器A | PA26 | PA25 | ❌ **不一致** | Encoder2_E2A |
| 右轮编码器B | PA25 | PA26 | ❌ **不一致** | Encoder2_E2B |
| 左轮编码器A | PB24 | PB20 | ❌ **不一致** | Encoder1_E1A |
| 左轮编码器B | PB20 | PB24 | ❌ **不一致** | Encoder1_E1B |
| **OLED显示** | | | | |
| OLED SCL | PA28 | PA28 | ✅ 一致 | 软件I2C时钟 |
| OLED SDA | PA31 | PA31 | ✅ 一致 | 软件I2C数据 |
| **按键** | | | | |
| 按键1 | PA8 | PA8 | ✅ 一致 | KEY0 |
| 按键2 | PB7 | - | ❌ **缺失** | 代码中未定义 |

---

## 🚨 **关键问题分析**

### **1. 电机方向控制引脚完全错乱**
```
接线图显示：          代码实际定义：
AIN1 → PA13          AIN1 → PA16  
AIN2 → PA14          AIN2 → PA17
BIN1 → PA16          BIN1 → PA14
BIN2 → PA17          BIN2 → PA13
```
**影响**：电机方向控制完全错误，会导致电机转向异常！

### **2. 编码器引脚交叉错误**
```
接线图显示：          代码实际定义：
右轮A → PA26         右轮A → PA25 (Encoder2_E2A)
右轮B → PA25         右轮B → PA26 (Encoder2_E2B)  
左轮A → PB24         左轮A → PB20 (Encoder1_E1A)
左轮B → PB20         左轮B → PB24 (Encoder1_E1B)
```
**影响**：编码器计数方向可能相反，影响速度和位置反馈！

### **3. 按键2缺失定义**
- 接线图中的PB7按键在代码中没有对应定义
- 可能导致按键功能无法使用

---

## ✅ **正确的引脚对照表**

### **根据代码实际定义，正确的接线应该是：**

| 功能 | 正确引脚 | TB6612/设备引脚 | 说明 |
|------|----------|-----------------|------|
| **电机驱动** | | | |
| 右电机PWM | PB2 | PWMA | ✅ |
| 右电机方向1 | PA16 | AIN1 | ⚠️ 接线图错误 |
| 右电机方向2 | PA17 | AIN2 | ⚠️ 接线图错误 |
| 左电机PWM | PB3 | PWMB | ✅ |
| 左电机方向1 | PA14 | BIN1 | ⚠️ 接线图错误 |
| 左电机方向2 | PA13 | BIN2 | ⚠️ 接线图错误 |
| **编码器** | | | |
| 右轮编码器A | PA25 | 编码器A相 | ⚠️ 接线图错误 |
| 右轮编码器B | PA26 | 编码器B相 | ⚠️ 接线图错误 |
| 左轮编码器A | PB20 | 编码器A相 | ⚠️ 接线图错误 |
| 左轮编码器B | PB24 | 编码器B相 | ⚠️ 接线图错误 |

---

## 🔧 **修正建议**

### **方案A：修改硬件接线（推荐）**
按照代码中的引脚定义重新接线：

```
TB6612电机驱动：
AIN1 → PA16 (不是PA13)
AIN2 → PA17 (不是PA14)  
BIN1 → PA14 (不是PA16)
BIN2 → PA13 (不是PA17)

编码器接线：
右轮A相 → PA25 (不是PA26)
右轮B相 → PA26 (不是PA25)
左轮A相 → PB20 (不是PB24)  
左轮B相 → PB24 (不是PB20)
```

### **方案B：修改代码配置**
如果硬件已经按接线图连接，需要修改SysConfig配置：

1. 打开`empty.syscfg`文件
2. 重新配置GPIO引脚分配
3. 重新生成`ti_msp_dl_config.h`

---

## ⚠️ **对测试的影响**

### **当前状态下进行电机测试的风险：**

1. **PWM测试**：✅ 可以正常进行（PB2/PB3正确）
2. **方向控制测试**：❌ 会出现异常（引脚错误）
3. **编码器测试**：❌ 计数方向可能相反
4. **控制算法测试**：❌ 整体控制逻辑错误

### **建议测试顺序：**

1. **先进行PWM波形测试**（PB2/PB3）- 安全
2. **暂停方向控制和编码器测试**
3. **修正引脚接线后再进行完整测试**

---

## 📋 **修正后的完整接线图**

```
电机驱动 (TB6612)：
PWMA → PB2  ✅
AIN1 → PA16 ⚠️ (原接线图写的PA13)
AIN2 → PA17 ⚠️ (原接线图写的PA14)
PWMB → PB3  ✅  
BIN1 → PA14 ⚠️ (原接线图写的PA16)
BIN2 → PA13 ⚠️ (原接线图写的PA17)

编码器：
右轮A相 → PA25 ⚠️ (原接线图写的PA26)
右轮B相 → PA26 ⚠️ (原接线图写的PA25)
左轮A相 → PB20 ⚠️ (原接线图写的PB24)
左轮B相 → PB24 ⚠️ (原接线图写的PB20)

其他模块：
MPU6050: SDA→PA0, SCL→PA1, INT→PA7 ✅
OLED: SDA→PA31, SCL→PA28 ✅
按键1: PA8 ✅
```

---

## 🎯 **结论**

**引脚定义存在严重不一致！** 主要问题集中在：
1. **电机方向控制引脚完全错乱**
2. **编码器引脚A/B相交换**  
3. **按键2定义缺失**

**强烈建议在进行电机控制测试前先修正这些引脚问题，否则测试结果将不可靠，甚至可能损坏硬件。**
