/*
 * 电机独立测试程序 - 验证接线是否正确
 * 功能：独立测试电机PWM和方向控制，不依赖MPU6050、编码器等其他模块
 * 作者：Alex (工程师)
 * 日期：2025-01-31
 */

#include "ti_msp_dl_config.h"
#include <stdlib.h>

// 延时函数
void delay_ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 8000; j++) {  // 基于80MHz时钟的粗略延时
            __NOP();
        }
    }
}

// 电机控制函数 - 简化版本，不依赖其他模块
void motor_test_pwm(int left_pwm, int right_pwm) {
    // 左电机方向控制 (AIN1/AIN2)
    if(left_pwm > 0) {
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);    // PA16=1
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);  // PA17=0
    } else if(left_pwm < 0) {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);  // PA16=0
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);    // PA17=1
        left_pwm = -left_pwm;  // 转为正值用于PWM
    } else {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);  // PA16=0
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);  // PA17=0
    }

    // 右电机方向控制 (BIN1/BIN2)
    if(right_pwm > 0) {
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);    // PA14=1
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);  // PA13=0
    } else if(right_pwm < 0) {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);  // PA14=0
        DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);    // PA13=1
        right_pwm = -right_pwm;  // 转为正值用于PWM
    } else {
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);  // PA14=0
        DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);  // PA13=0
    }

    // PWM输出 (限制在0-7000范围内，避免过大电流)
    if(left_pwm > 7000) left_pwm = 7000;
    if(right_pwm > 7000) right_pwm = 7000;
    
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, right_pwm, GPIO_PWM_0_C0_IDX);  // PB2
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, left_pwm, GPIO_PWM_0_C1_IDX);   // PB3
}

// LED状态指示
void led_toggle(void) {
    DL_GPIO_togglePins(LED_PORT, LED_UserLED_PIN);
}

// 按键检测 (PA8)
uint8_t key_pressed(void) {
    return (DL_GPIO_readPins(GPIO_GRP_0_KEY0_PORT, GPIO_GRP_0_KEY0_PIN) == 0);
}

// 电机测试序列
void motor_test_sequence(void) {
    // 测试1: 停止状态
    motor_test_pwm(0, 0);
    delay_ms(1000);
    
    // 测试2: 左电机正转 (低速)
    motor_test_pwm(2000, 0);
    delay_ms(2000);
    
    // 测试3: 左电机反转 (低速)
    motor_test_pwm(-2000, 0);
    delay_ms(2000);
    
    // 测试4: 右电机正转 (低速)
    motor_test_pwm(0, 2000);
    delay_ms(2000);
    
    // 测试5: 右电机反转 (低速)
    motor_test_pwm(0, -2000);
    delay_ms(2000);
    
    // 测试6: 双电机同向 (前进)
    motor_test_pwm(2000, 2000);
    delay_ms(2000);
    
    // 测试7: 双电机反向 (后退)
    motor_test_pwm(-2000, -2000);
    delay_ms(2000);
    
    // 测试8: 双电机差速 (转向)
    motor_test_pwm(2000, 1000);
    delay_ms(2000);
    
    // 测试9: 停止
    motor_test_pwm(0, 0);
    delay_ms(1000);
}

int main(void) {
    // 系统初始化
    SYSCFG_DL_init();
    
    // 启动PWM定时器
    DL_TimerA_startCounter(PWM_0_INST);
    
    // 初始状态：电机停止
    motor_test_pwm(0, 0);
    
    // LED指示系统启动
    for(int i = 0; i < 5; i++) {
        led_toggle();
        delay_ms(200);
    }
    
    uint8_t test_running = 0;
    uint32_t led_timer = 0;
    
    while(1) {
        // LED心跳指示 (系统正常运行)
        led_timer++;
        if(led_timer >= 50000) {  // 约500ms
            led_toggle();
            led_timer = 0;
        }
        
        // 按键控制测试
        if(key_pressed() && !test_running) {
            test_running = 1;
            
            // 快速闪烁LED表示开始测试
            for(int i = 0; i < 10; i++) {
                led_toggle();
                delay_ms(100);
            }
            
            // 执行电机测试序列
            motor_test_sequence();
            
            test_running = 0;
            
            // 测试完成，LED常亮2秒
            DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
            delay_ms(2000);
            DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
        }
        
        // 防止按键连续触发
        if(key_pressed()) {
            delay_ms(50);  // 消抖
        }
    }
}

/*
 * 测试说明：
 * 
 * 1. 上电后LED会快速闪烁5次，表示系统启动完成
 * 2. 之后LED以约1Hz频率闪烁，表示系统正常运行
 * 3. 按下PA8按键开始电机测试
 * 4. 测试开始时LED快速闪烁10次
 * 5. 测试序列：
 *    - 停止 (1秒)
 *    - 左电机正转 (2秒)
 *    - 左电机反转 (2秒)  
 *    - 右电机正转 (2秒)
 *    - 右电机反转 (2秒)
 *    - 双电机前进 (2秒)
 *    - 双电机后退 (2秒)
 *    - 差速转向 (2秒)
 *    - 停止 (1秒)
 * 6. 测试完成后LED常亮2秒
 * 7. 可重复按键进行测试
 * 
 * 预期现象：
 * - 如果接线正确：电机按序列转动，方向正确
 * - 如果方向引脚错误：电机转向与预期相反或不转
 * - 如果PWM引脚错误：电机不转或转动异常
 * - 如果电源问题：电机无力或系统重启
 * 
 * 安全特性：
 * - PWM限制在7000以下 (约87%占空比)
 * - 每次测试前都会停止电机
 * - 低速测试，避免机械损伤
 */
