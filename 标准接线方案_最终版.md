# 🔌 MSPM0小车标准接线方案 - 最终版

## ⚡ **重要声明**
**请严格按照此接线方案连接，这是基于代码实际定义的标准方案！**

---

## 🚗 **电机驱动系统 (TB6612) - 核心部分**

### **电源连接**
| TB6612引脚 | 连接目标 | 电压 |
|------------|----------|------|
| VM         | 电源模块2的12V | 12V |
| VCC        | 电源模块1的5V  | 5V |
| GND (×3)   | 电源模块1的GND | 0V |
| STBY       | 电源模块1的5V  | 5V (常高) |

### **PWM控制信号**
| TB6612引脚 | MSPM0引脚 | 功能说明 |
|------------|-----------|----------|
| **PWMA**   | **PB2**   | 右电机PWM (10kHz) |
| **PWMB**   | **PB3**   | 左电机PWM (10kHz) |

### **方向控制信号 ⚠️ 关键修正**
| TB6612引脚 | MSPM0引脚 | 功能说明 | 原接线图错误 |
|------------|-----------|----------|--------------|
| **AIN1**   | **PA16**  | 右电机方向1 | ❌ 原写PA13 |
| **AIN2**   | **PA17**  | 右电机方向2 | ❌ 原写PA14 |
| **BIN1**   | **PA14**  | 左电机方向1 | ❌ 原写PA16 |
| **BIN2**   | **PA13**  | 左电机方向2 | ❌ 原写PA17 |

### **电机连接**
| TB6612引脚 | 连接目标 |
|------------|----------|
| AO1        | 右轮电机正极 |
| AO2        | 右轮电机负极 |
| BO1        | 左轮电机正极 |
| BO2        | 左轮电机负极 |

---

## 🔄 **编码器系统 ⚠️ 关键修正**

| 编码器 | MSPM0引脚 | 功能 | 原接线图错误 |
|--------|-----------|------|--------------|
| **右轮编码器A相** | **PA25** | Encoder2_E2A | ❌ 原写PA26 |
| **右轮编码器B相** | **PA26** | Encoder2_E2B | ❌ 原写PA25 |
| **左轮编码器A相** | **PB20** | Encoder1_E1A | ❌ 原写PB24 |
| **左轮编码器B相** | **PB24** | Encoder1_E1B | ❌ 原写PB20 |

---

## 📐 **MPU6050陀螺仪 ✅ 正确**

| MPU6050引脚 | MSPM0引脚 | 功能 |
|-------------|-----------|------|
| VCC         | 5V        | 电源 |
| GND         | GND       | 地线 |
| **SDA**     | **PA0**   | I2C数据线 |
| **SCL**     | **PA1**   | I2C时钟线 |
| **INT**     | **PA7**   | 中断信号 |

---

## 🔍 **灰度传感器 (6路) ✅ 正确**

| 灰度传感器 | MSPM0引脚 | ADC通道 |
|------------|-----------|---------|
| VCC        | 5V        | 电源 |
| GND        | GND       | 地线 |
| OUT1       | PA9       | ADC |
| OUT3       | PA27      | ADC |
| OUT4       | PA24      | ADC |
| OUT5       | PB16      | ADC |
| OUT6       | PA12      | ADC |
| OUT8       | PB6       | ADC |

---

## 🖥️ **OLED显示屏 ✅ 正确**

| OLED引脚 | MSPM0引脚 | 功能 |
|----------|-----------|------|
| VCC      | 3.3V      | 电源 |
| GND      | GND       | 地线 |
| **SCL**  | **PA28**  | 软件I2C时钟 |
| **SDA**  | **PA31**  | 软件I2C数据 |

---

## 🎮 **按键系统**

| 按键 | MSPM0引脚 | 触发方式 |
|------|-----------|----------|
| 按键1 | PA8       | 接GND表示按下 |
| 按键2 | PB7       | 接3.3V表示按下 |

---

## ⚡ **电源系统**

### **电源模块1 (5V供电)**
- **输入**: 电池正负极
- **输出**: 5V/GND 供给主控板、传感器、逻辑电路

### **电源模块2 (12V供电)**  
- **输入**: 电池正负极
- **输出**: 12V 专供TB6612电机驱动

---

## 🔧 **代码逻辑验证**

### **电机控制逻辑 (balance.c 第137-163行)**
```c
// 左电机控制 (对应AIN1/AIN2)
if(left>0) {
    DL_GPIO_setPins(MOTOR_DIR_PORT,MOTOR_DIR_AIN1_PIN);    // PA16=1
    DL_GPIO_clearPins(MOTOR_DIR_PORT,MOTOR_DIR_AIN2_PIN);  // PA17=0
} else {
    DL_GPIO_clearPins(MOTOR_DIR_PORT,MOTOR_DIR_AIN1_PIN);  // PA16=0  
    DL_GPIO_setPins(MOTOR_DIR_PORT,MOTOR_DIR_AIN2_PIN);    // PA17=1
}

// 右电机控制 (对应BIN1/BIN2)
if(right>0) {
    DL_GPIO_setPins(MOTOR_DIR_PORT,MOTOR_DIR_BIN1_PIN);    // PA14=1
    DL_GPIO_clearPins(MOTOR_DIR_PORT,MOTOR_DIR_BIN2_PIN);  // PA13=0
} else {
    DL_GPIO_clearPins(MOTOR_DIR_PORT,MOTOR_DIR_BIN1_PIN);  // PA14=0
    DL_GPIO_setPins(MOTOR_DIR_PORT,MOTOR_DIR_BIN2_PIN);    // PA13=1
}

// PWM输出
DL_TimerA_setCaptureCompareValue(PWM_0_INST,abs(right),GPIO_PWM_0_C0_IDX); // PB2
DL_TimerA_setCaptureCompareValue(PWM_0_INST,abs(left),GPIO_PWM_0_C1_IDX);  // PB3
```

### **编码器中断逻辑 (empty.c 第147-202行)**
```c
// 编码器1 (左轮) - PB20/PB24
if((portE1_intp&Encoder1_E1A_PIN)==Encoder1_E1A_PIN) {  // PB20中断
    if(DL_GPIO_readPins(Encoder1_PORT,Encoder1_E1B_PIN)>0) // 读PB24
        g_EncoderACount--;
    else 
        g_EncoderACount++;
}

// 编码器2 (右轮) - PA25/PA26  
if((portE2_intp&Encoder2_E2A_PIN)==Encoder2_E2A_PIN) {  // PA25中断
    if(DL_GPIO_readPins(Encoder2_PORT,Encoder2_E2B_PIN)>0) // 读PA26
        g_EncoderBCount++;
    else 
        g_EncoderBCount--;
}
```

---

## ✅ **为什么按代码定义接线？**

1. **代码逻辑已验证**：电机控制和编码器处理逻辑完全基于这些引脚
2. **中断配置匹配**：编码器中断配置与引脚定义一致
3. **PWM输出正确**：PB2/PB3的PWM配置已验证
4. **避免重新编程**：不需要修改和重新编译代码
5. **系统稳定性**：原代码经过测试，引脚定义可靠

---

## ⚠️ **接线检查清单**

### **修正前 vs 修正后对比**
| 功能 | 原接线图 | 正确接线 | 状态 |
|------|----------|----------|------|
| 右电机AIN1 | PA13 | **PA16** | ⚠️ 需修正 |
| 右电机AIN2 | PA14 | **PA17** | ⚠️ 需修正 |
| 左电机BIN1 | PA16 | **PA14** | ⚠️ 需修正 |
| 左电机BIN2 | PA17 | **PA13** | ⚠️ 需修正 |
| 右轮编码器A | PA26 | **PA25** | ⚠️ 需修正 |
| 右轮编码器B | PA25 | **PA26** | ⚠️ 需修正 |
| 左轮编码器A | PB24 | **PB20** | ⚠️ 需修正 |
| 左轮编码器B | PB20 | **PB24** | ⚠️ 需修正 |

### **接线完成后验证步骤**
1. ✅ 检查电源电压 (5V±0.1V, 12V±0.5V)
2. ✅ 验证PWM输出 (PB2/PB3, 10kHz)
3. ✅ 测试I2C通信 (PA0/PA1, MPU6050响应)
4. ✅ 验证编码器中断 (手动转动计数)
5. ✅ 测试电机方向控制 (正反转正确)

---

## 🎯 **总结**

**请严格按照此标准接线方案连接硬件！**

这是基于代码实际定义的唯一正确方案，确保：
- 电机控制逻辑正确
- 编码器反馈准确  
- 系统稳定运行
- 测试结果可靠

**修正这8个引脚连接后，您的电机控制测试将能够正常进行！**
